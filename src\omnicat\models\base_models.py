from abc import ABC, abstractmethod
from pydantic import BaseModel, Field, conlist, field_validator
from typing import Any, <PERSON><PERSON><PERSON><PERSON>
from datetime import datetime, timedelta, timezone
import re
import math
from orbitalopsengine.physics.propagation.tle_rv import rv2tle, tle2rv
from orbitalopsengine.physics.propagation.two_line_element import TLE as OoeTle
from orbitalopsengine.physics.quantities.vector3d import Vector3D as OoeVector3D
from orbitalopsengine.physics.quantities.quantities import kilometer, kilometer_per_second
from orbitalopsengine.physics.reference_frames.reference_frame import ECI


class ErrorResponse(BaseModel):
    code: int | None = Field(..., description="The error code.")
    message: str = Field(..., description="A message indicating the error.")


Vector3D: TypeAlias = conlist(float, min_length=3, max_length=3)
Metadata: TypeAlias = dict[str, Any]
ParsedTle: TypeAlias = OoeTle


class TrackData(ABC):
    @property
    @abstractmethod
    def validity_time(self) -> datetime:
        pass


class StateVector(BaseModel, TrackData):
    """
    A state vector (or RVT) represents a timestamped position and velocity
    in the inertial reference frame (ECI). Optional fields include attitude
    (Euler angles in radians) and angular velocity (rate of change of attitude
    in radians per second). Position is in kilometers, velocity in kilometers
    per second, and epoch as an ISO 8601 datetime.
    """
    timestamp: datetime = Field(...,
                                description="Timestamp in ISO 8601 format.")
    position: Vector3D = Field(...,
                               description="Position in kilometers (ECI frame).")
    velocity: Vector3D = Field(
        ..., description="Velocity in kilometers per second (ECI frame).")
    attitude: Vector3D | None = Field(
        None, description="Euler angles in radians.")
    angular_velocity: Vector3D | None = Field(
        None, description="Rate of change of attitude in rad/s.")

    @property
    def validity_time(self) -> datetime:
        return self.timestamp


class TLE(BaseModel, TrackData):
    """
    A Two-Line Element (TLE) set describes the orbit of an object using
    two lines of text. The TLE format is widely used to describe satellite
    orbits and includes information such as the object's NORAD catalog number,
    orbital elements, and epoch. The epoch is in UTC time.
    """
    line1: str = Field(..., description="First line of the TLE set.")
    line2: str = Field(..., description="Second line of the TLE set.")

    @field_validator('line1')
    @classmethod
    def validate_line1(cls, v: str) -> str:
        # Check length
        if len(v) != 69:
            raise ValueError("Line 1 must be exactly 69 characters long")

        # Check starts with '1 '
        if not v.startswith('1 '):
            raise ValueError("Line 1 must start with '1 '")

        # Check epoch format using regex
        epoch_str = v[18:32]
        epoch_pattern = r'^\d{2}(\d{3}\.\d{8})$'
        if not re.match(epoch_pattern, epoch_str):
            raise ValueError("Invalid epoch format in line 1")

        # Validate epoch values
        try:
            year = int(epoch_str[0:2])
            day_of_year = float(epoch_str[2:])

            if day_of_year < 1 or day_of_year >= 367:  # Account for leap years
                raise ValueError("Day of year must be between 1 and 366")

            # Test if we can actually create the datetime
            if year < 57:
                year += 2000
            else:
                year += 1900
            base_date = datetime(year, 1, 1)
            base_date + timedelta(days=day_of_year - 1)

        except ValueError as e:
            raise ValueError(f"Invalid epoch values: {str(e)}")

        return v

    @field_validator('line2')
    @classmethod
    def validate_line2(cls, v: str) -> str:
        if len(v) != 69:
            raise ValueError("Line 2 must be exactly 69 characters long")
        if not v.startswith('2 '):
            raise ValueError("Line 2 must start with '2 '")
        return v

    @property
    def validity_time(self) -> datetime:
        """
        Extract and return the validity time as a datetime object.
        This time is encoded in characters 19-32 of line 1.
        Format is YYDDD.DDDDDDDD where:
        - YY is the 2-digit year
        - DDD is the 3-digit day of year
        - DDDDDDDD is the fractional part of the day
        """
        epoch_str = self.line1[18:32]  # We know this is valid due to validators
        year = int(epoch_str[0:2])

        if year < 57:
            year += 2000
        else:
            year += 1900

        day_of_year = float(epoch_str[2:])
        base_date = datetime(year, 1, 1)
        return base_date + timedelta(days=day_of_year - 1)

    def parse(self) -> ParsedTle:
        return ParsedTle.from_strings(self.line1, self.line2)

    def to_state_vector(self) -> StateVector:
        position, velocity = tle2rv(self.parse())
        return StateVector(
            timestamp=self.validity_time,
            position=position.to(kilometer).as_array(),
            velocity=velocity.to(kilometer_per_second).as_array(),
            attitude=None,
            angular_velocity=None
        )

    @classmethod
    def from_state_vector(cls, state_vector: StateVector) -> "TLE":
        parsed_tle = rv2tle(
            OoeVector3D.from_array(
                state_vector.position, unit=kilometer, reference_frame=ECI),
            OoeVector3D.from_array(
                state_vector.velocity, unit=kilometer_per_second, reference_frame=ECI),
            epoch=state_vector.timestamp
        )
        return TLE(line1=str(parsed_tle.line1), line2=str(parsed_tle.line2))


class AreaOfInterest(BaseModel):
    """
    An area of interest represents a spherical area in space over a duration of time. 
    Position components are in kilometers in geodetic coordinates (lat, lon, alt), with the altitude in kilometers.
    Radius is in kilometers, and time components are in ISO 8601 format (UTC).
    """
    center: Vector3D = Field(...,
                             description="Geodetic center of the aoi (lat, lon, alt) with altitude in kilometers.")
    radius: float = Field(..., description="Radius in kilometers.")
    time_start: datetime = Field(...,
                                 description="Start time in ISO 8601 format.")
    time_end: datetime = Field(..., description="End time in ISO 8601 format.")

    @field_validator('time_start', 'time_end')
    @classmethod
    def ensure_utc(cls, v: datetime) -> datetime:
        if v.tzinfo is None:
            v = v.replace(tzinfo=timezone.utc)
        return v.astimezone(timezone.utc)

    @property
    def time_center(self) -> datetime:
        return self.time_start + (self.time_end - self.time_start) / 2


class StatusMessage(BaseModel):
    message: str = Field(...,
                         description="A message indicating the status of the operation.")


class Pagination(BaseModel):
    page: int = Field(...,
                      description="Page number for pagination (0-based index).", ge=0)
    page_size: int = Field(...,
                           description="Number of records per page.", gt=0)
    total_pages: int = Field(..., description="Total number of pages.", gt=0)
    total_records: int = Field(...,
                               description="Total number of records.", ge=0)

    @staticmethod
    def paginate(records: list, page: int, page_size: int) -> tuple["Pagination", list]:
        # Calculate pagination info
        total_records = len(records)
        total_pages = max(1, math.ceil(
            total_records / page_size))  # At least 1 page

        # Apply pagination
        start_idx = page * page_size
        end_idx = start_idx + page_size
        paginated_records = records[start_idx:end_idx]

        # Create pagination info
        pagination = Pagination(
            page=page,
            page_size=page_size,
            total_pages=total_pages,
            total_records=max(0, total_records)  # Ensure non-negative
        )

        return pagination, paginated_records
