import pytest
from pytest import approx
from datetime import datetime
from pydantic import ValidationError
from omnicat.models.base_models import TLE


def test_valid_tle_creation():
    # ISS TLE from January 10, 2024
    tle = TLE(
        line1="1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992",
        line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
    )

    # Test validity_time property
    expected_date = datetime(2024, 1, 10, 12, 24, 15, 399619)
    # Allow 1 second difference due to float precision
    assert abs((tle.validity_time - expected_date).total_seconds()) < 1


def test_invalid_line1_length():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            line1="1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  999",  # 68 chars
            line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
        )
    assert "Line 1 must be exactly 69 characters long" in str(exc_info.value)


def test_invalid_line2_length():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            line1="1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992",
            line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.4955490043153"  # 68 chars
        )
    assert "Line 2 must be exactly 69 characters long" in str(exc_info.value)


def test_invalid_line1_prefix():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            # Starts with 0
            line1="0 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992",
            line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
        )
    assert "Line 1 must start with '1 '" in str(exc_info.value)


def test_invalid_line2_prefix():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            line1="1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  9992",
            line2="1 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"  # Starts with 1
        )
    assert "Line 2 must start with '2 '" in str(exc_info.value)


def test_invalid_epoch_format():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            # Invalid day format
            line1="1 25544U 98067A   2401A.51684028  .00014877  00000+0  27569-3 0  9992",
            line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
        )
    assert "Invalid epoch format in line 1" in str(exc_info.value)


def test_invalid_day_of_year():
    with pytest.raises(ValidationError) as exc_info:
        TLE(
            line1="1 25544U 98067A   24367.51684028  .00014877  00000+0  27569-3 0  9992",  # Day 367
            line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
        )
    assert "Day of year must be between 1 and 366" in str(exc_info.value)


def test_pre_1957_year():
    # Test a TLE with year 56 (should be interpreted as 2056)
    tle = TLE(
        line1="1 25544U 98067A   56010.51684028  .00014877  00000+0  27569-3 0  9992",
        line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
    )
    assert tle.validity_time.year == 2056


def test_post_1957_year():
    # Test a TLE with year 57 (should be interpreted as 1957)
    tle = TLE(
        line1="1 25544U 98067A   57010.51684028  .00014877  00000+0  27569-3 0  9992",
        line2="2 25544  51.6429 169.3329 0005151  32.6226 144.5329 15.49554900431533"
    )
    assert tle.validity_time.year == 1957


def test_state_vector_conversion():
    # ISS (ZARYA)
    # https://en.wikipedia.org/wiki/Two-line_element_set#Format
    tle = TLE(
        line1="1 25544U 98067A   08264.51782528 -.00002182  00000-0 -11606-4 0  2927",
        line2="2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.72125391563537"
    )

    state_vector = tle.to_state_vector()

    assert state_vector.timestamp == tle.validity_time

    roundtrip_tle = TLE.from_state_vector(state_vector).parse()

    # Compare non-orbital parameters (line 1)
    tol = 1e-6
    assert roundtrip_tle.line1.epoch.to_datetime().timestamp(
    ) == approx(tle.validity_time.timestamp(), abs=1.0)

    # Compare orbital elements (line 2)
    assert roundtrip_tle.line2.inclination_deg == approx(51.6416, abs=tol)
    assert roundtrip_tle.line2.raan_deg == approx(247.4627, abs=tol)
    # # decimal point assumed for eccentricity
    assert abs(roundtrip_tle.line2.eccentricity - 6703) <= 1
    assert roundtrip_tle.line2.argument_of_perigee == approx(130.5360, abs=tol)
    assert roundtrip_tle.line2.mean_anomaly == approx(325.0288, abs=tol)
    assert roundtrip_tle.line2.mean_motion == approx(15.72125391, abs=tol)


def test_calculate_checksum():
    """Test the TLE checksum calculation method."""
    # Test case 1: Line with digits and minus signs
    line1 = "1 25544U 98067A   24010.51684028  .00014877  00000+0  27569-3 0  999"
    checksum1 = TLE._calculate_checksum(line1)
    assert checksum1 == "9"  # Expected checksum for this line

    # Test case 2: Line with minus signs (should count as 1)
    line2 = "1 25544U 98067A   08264.51782528 -.00002182  00000-0 -11606-4 0  292"
    checksum2 = TLE._calculate_checksum(line2)
    assert checksum2 == "7"  # Expected checksum for this line

    # Test case 3: Welders arc test data
    line3 = "1 28814U          25096.33110089 +.00000000  88294-1  43288 0 4 0001"
    checksum3 = TLE._calculate_checksum(line3)
    assert checksum3 == "4"  # Expected checksum for this line

    line4 = "2 28814  26.3177 173.3992 6772806  53.7416   0.8687  2.5997730900001"
    checksum4 = TLE._calculate_checksum(line4)
    assert checksum4 == "8"  # Expected checksum for this line

    # Test case 4: Line with only one minus sign (edge case)
    line5 = "A BCDEF GHIJK   LMNOP.QRSTUVWX  .YZABCDEF  GHIJK+L  MNOPQ-R S  ABCD"
    checksum5 = TLE._calculate_checksum(line5)
    assert checksum5 == "1"  # Only one minus sign counts as 1


def test_calculate_checksum_known_examples():
    """Test checksum calculation with known TLE examples."""
    # ISS TLE from Wikipedia (without checksum)
    # https://en.wikipedia.org/wiki/Two-line_element_set#Format
    line1_no_checksum = "1 25544U 98067A   08264.51782528 -.00002182  00000-0 -11606-4 0  292"
    expected_checksum1 = "7"
    assert TLE._calculate_checksum(line1_no_checksum) == expected_checksum1

    line2_no_checksum = "2 25544  51.6416 247.4627 0006703 130.5360 325.0288 15.7212539156353"
    expected_checksum2 = "7"
    assert TLE._calculate_checksum(line2_no_checksum) == expected_checksum2
