import unittest
from datetime import datetime
from uuid import uuid4

from omnicat.ingestor.welders_arc.models import Elset_Sgp4Xp, MessageHeader
from omnicat.ingestor.welders_arc.welders_arc_ingestor import get_tracks
from omnicat.models import Track, ObjectCorrelation
from omnicat.models.base_models import TLE


class TestWeldersArcIngestor(unittest.TestCase):
    def test_get_tracks(self):
        # Create sample data
        elset_sgp4xp = Elset_Sgp4Xp(
            idElset="0af401eb-e6ec-4af5-95d5-6b59bcb1d172",
            classificationMarking="U",
            satNo="28814",
            epoch="2025-04-06T07:56:47.109Z",
            # 68 chars, checksum will be auto-added
            line1="1 28814U          25096.33110089 +.00000000  88294-1  43288 0 4 0001",
            # 68 chars, checksum will be auto-added
            line2="2 28814  26.3177 173.3992 6772806  53.7416   0.8687  2.5997730900001",
            createdAt="2025-04-08T01:44:59.681",
            createdBy="producer.pulsar",
            origin="COMSPOC",
            source="Space Book",
            ephemType=4,
            uct=False,
            origObjectId="28814",
            dataMode="REAL",
            algorithm="SGP4-XP",
            origNetwork="SDA TAP"
        )

        # Create sample message header
        header = MessageHeader(
            messageId=str(uuid4()),
            messageTime=datetime.now(),
            messageVersion="1.0",
            subsystem="ss2",
            dataProvider="welders_arc",
            dataType="ss2.data.elset.sgp4-xp",
            dataVersion="1.0"
        )

        # Test without object correlation
        tracks = get_tracks([elset_sgp4xp], [header])

        # Verify results
        self.assertEqual(len(tracks), 1)
        track = tracks[0]

        # Check track properties
        self.assertEqual(track.source, "welders_arc")
        self.assertEqual(track.format, "tle")
        self.assertIsInstance(track.data, TLE)

        # Check that line1 equals the original line plus its checksum
        checksum1 = TLE._calculate_checksum(elset_sgp4xp.line1)
        self.assertEqual(track.data.line1, f"{elset_sgp4xp.line1}{checksum1}")

        # Check that line2 equals the original line plus its checksum
        checksum2 = TLE._calculate_checksum(elset_sgp4xp.line2)
        self.assertEqual(track.data.line2, f"{elset_sgp4xp.line2}{checksum2}")

        # Check metadata
        self.assertIn("welders_arc_elset", track.metadata)
        self.assertIn("welders_arc_message_headers", track.metadata)

        object_ids = ["object-123"]

        tracks_with_corr = get_tracks(
            [elset_sgp4xp], [header],
            object_ids=object_ids
        )

        # Verify results with object correlation
        self.assertEqual(len(tracks_with_corr), 1)
        track_with_corr = tracks_with_corr[0]

        # Check lines with checksums in the object correlation case too
        self.assertEqual(track_with_corr.data.line1,
                         f"{elset_sgp4xp.line1}{checksum1}")
        self.assertEqual(track_with_corr.data.line2,
                         f"{elset_sgp4xp.line2}{checksum2}")

        # Check object correlations
        self.assertIsNotNone(track_with_corr.object_correlations)
        self.assertEqual(len(track_with_corr.object_correlations), 1)
        self.assertIsInstance(
            track_with_corr.object_correlations[0], ObjectCorrelation)
        self.assertEqual(
            track_with_corr.object_correlations[0].object_id, "object-123")
        self.assertEqual(
            track_with_corr.object_correlations[0].confidence, 1.0)


if __name__ == '__main__':
    unittest.main()
