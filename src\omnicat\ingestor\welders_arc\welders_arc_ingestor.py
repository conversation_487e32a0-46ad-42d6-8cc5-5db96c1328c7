import json
from kafka import KafkaConsumer
from kafka.consumer.fetcher import ConsumerRecord
from datetime import datetime, timezone
import time
from pydantic import BaseModel

from omnicat import Settings, get_settings, logger
from omnicat.ingestor import OmnicatIngestor
from omnicat.ingestor.welders_arc.models import MessageHeader, Elset_Sgp4Xp, Elset_UctCandidate
from omnicat.models import Track, TLE, ObjectCorrelation, Object
from omnicat.ingestor.omnicat_ingestor import get_ingestion_time_window
from omnicat.ingestor.udl import UdlReader, OnOrbit_Abridged

ELSET_TYPES = {
    'ss2.data.elset.sgp4-xp': Elset_Sgp4Xp,
    'ss2.data.elset.uct-candidate': Elset_UctCandidate
}

WELDERS_ARC_SOURCE_NAME = "welders_arc"


def main() -> None:
    try:
        settings = get_settings()
        logger.info(f"\n\nsettings: {settings}\n\n")

        welders_arc_ingestor = WeldersArcIngestor.from_settings(settings)

        poll_timeout_seconds = settings.welders_arc_kafka.poll_timeout_ms / 1000

        while True:
            now = datetime.now(timezone.utc)

            window_start, window_end = get_ingestion_time_window(
                now,
                settings.welders_arc_kafka.ingestion_window_days_back,
                settings.welders_arc_kafka.ingestion_window_days_forward
            )

            t_start = time.time()

            messages_received = welders_arc_ingestor.ingest(
                window_start, window_end)

            welders_arc_ingestor.clear_stale_state_data(
                window_start, window_end)

            t_end = time.time()

            next_poll_time = t_start + poll_timeout_seconds
            if messages_received == 0 and t_end < next_poll_time:
                time.sleep(next_poll_time - t_end)

    except Exception as e:
        logger.error(f"error: {e}")


class WeldersArcIngestor:
    def __init__(self, consumer: KafkaConsumer, omnicat_ingestor: OmnicatIngestor, poll_timeout_ms: int, batch_size: int, udl_reader: UdlReader):
        self.consumer: KafkaConsumer = consumer
        self.omnicat_ingestor: OmnicatIngestor = omnicat_ingestor
        self.poll_timeout_ms: int = poll_timeout_ms
        self.batch_size: int = batch_size
        self.udl_reader: UdlReader = udl_reader

    @staticmethod
    def from_settings(settings: Settings) -> "WeldersArcIngestor":
        consumer = KafkaConsumer(
            *settings.welders_arc_kafka.elset_topics,
            bootstrap_servers=settings.welders_arc_kafka.brokers,
            security_protocol=settings.welders_arc_kafka.security_protocol,
            sasl_mechanism=settings.welders_arc_kafka.sasl_mechanism,
            sasl_plain_username=settings.welders_arc_kafka.sasl_plain_username,
            sasl_plain_password=settings.welders_arc_kafka.sasl_plain_password,
            auto_offset_reset=settings.welders_arc_kafka.auto_offset_reset,
            enable_auto_commit=settings.welders_arc_kafka.enable_auto_commit,
            group_id=settings.welders_arc_kafka.group_id,
            value_deserializer=lambda v: json.loads(v.decode('utf-8')),
        )
        udl_ingestor = UdlReader.from_settings(settings.udl)
        return WeldersArcIngestor(
            consumer,
            OmnicatIngestor.from_settings(settings),
            settings.welders_arc_kafka.poll_timeout_ms,
            settings.welders_arc_kafka.batch_size,
            udl_ingestor
        )

    def ingest(self, window_start: datetime, window_end: datetime) -> int:
        try:
            messages = self.consumer.poll(
                timeout_ms=self.poll_timeout_ms, max_records=self.batch_size)

            messages_received = sum(len(msgs) for msgs in messages.values())
            logger.info(
                f"Received {messages_received} messages from Kafka")

            if messages_received == 0:
                return 0

            self.ingest_kafka_messages(messages, window_start, window_end)

            return messages_received

        except Exception as e:
            logger.error(f"Error polling Kafka: {e}", exc_info=True)

    def ingest_kafka_messages(self, messages: list[ConsumerRecord], window_start: datetime, window_end: datetime) -> None:
        for topic_partition, topic_messages in messages.items():
            topic = topic_partition.topic
            logger.info(f"Processing messages from topic {topic}")

            if topic not in ELSET_TYPES:
                logger.warning(f"No handler found for topic: {topic}")
                continue

            logger.info(
                f"got messages type type(topic_messages): {type(topic_messages)}")
            logger.info(f"parsing messages as type: {ELSET_TYPES[topic]}")

            try:
                message_headers = parse_headers(topic_messages)
                elsets = parse_elsets(ELSET_TYPES[topic], topic_messages)

                ingested_tracks = self.ingest_elset_messages(elsets,
                                                             message_headers, window_start, window_end)
                logger.info(
                    f"Ingested {ingested_tracks} tracks from {topic}")

            except Exception as e:
                logger.error(
                    f"Error processing messages: {e}", exc_info=True)

    def ingest_elset_messages(self, elsets: list[BaseModel], headers: list[MessageHeader], window_start: datetime, window_end: datetime) -> int:

        objects = self.get_correlated_omnicat_objects(elsets)

        object_ids = self.omnicat_ingestor.ingest_objects(objects)

        tracks = get_tracks(elsets, headers, object_ids)

        self.omnicat_ingestor.ingest_tracks(tracks, window_start, window_end)

        return len(tracks)

    def clear_stale_state_data(self, window_start: datetime, window_end: datetime) -> None:
        self.omnicat_ingestor.clear_stale_state_data(
            window_start, window_end)

    def get_correlated_omnicat_objects(self, elsets: list[BaseModel]) -> list[Object]:
        onorbit_ids = [onorbit_id for elset in elsets
                       if (onorbit_id := elset.getIdOnOrbit()) is not None]

        if len(onorbit_ids) == 0:  # all uncorrelated tracks
            return []

        onorbits = self.get_correlated_onorbits(onorbit_ids)
        objects = self.get_omnicat_objects(onorbits)
        return objects

    def get_correlated_onorbits(self, onorbit_ids: list[str]) -> list[OnOrbit_Abridged]:
        onorbits = self.udl_reader.get_onorbits(onorbit_ids)
        logger.info(f"got {len(onorbits)} onorbits")
        return onorbits

    def get_omnicat_objects(self, onorbits: list[OnOrbit_Abridged]) -> list[Object]:
        objects = [Object(
            source=WELDERS_ARC_SOURCE_NAME,
            type=onorbit.objectType,
            data_uris=[self.udl_reader.get_onorbit_uri(onorbit)],
            country_code=onorbit.countryCode,
            common_name=onorbit.commonName,
            metadata={
                "udl_onorbit": onorbit.model_dump(mode='json')
            }
        ) for onorbit in onorbits]

        logger.info(f"got {len(objects)} objects")

        return objects


def parse_headers(messages: list) -> list[MessageHeader]:
    """Extract and parse message headers into MessageHeader objects for all messages."""
    def parse_headers(message):
        if not message.headers:
            return None

        try:
            header_dict = {
                k: json.loads(v.decode('utf-8').strip('"')) if k == 'traceability'
                else v.decode('utf-8').strip('"')
                for k, v in message.headers
            }
            return MessageHeader.model_validate(header_dict)
        except Exception as e:
            logger.warning(f"Error parsing message headers: {e}")
            return None

    return [header for header in (parse_headers(msg) for msg in messages) if header is not None]


def parse_elsets(model_type: BaseModel, messages: list) -> list[BaseModel]:
    return [validate_pydantic_model(model_type, message) for message in messages]


def validate_pydantic_model(model_type: BaseModel, message) -> BaseModel | None:
    """Helper function to validate an Elset message with error handling."""
    try:
        return model_type.model_validate(message.value)
    except Exception as e:
        logger.error(
            f"Error validating Elset message: {e}", exc_info=True)
        return None


def get_tracks(elsets: list[BaseModel], headers: list[MessageHeader], object_ids: list[str] = None) -> list[Track]:

    tracks = [Track(
        source=elset.source,
        format="tle",
        data=TLE(
            line1=elset.line1,
            line2=elset.line2
        ),
        metadata={
            "welders_arc_elset": elset.model_dump(mode='json'),
            "welders_arc_message_headers": header.model_dump(mode='json')
        }
    ) for elset, header in zip(elsets, headers)]

    correlated_indices = [i for i, elset in enumerate(
        elsets) if elset.getIdOnOrbit() is not None]

    if object_ids:
        for i, object_id in zip(correlated_indices, object_ids):
            tracks[i].object_correlations = [ObjectCorrelation(
                object_id=object_id,
                validity_time=headers[i].messageTime,
                confidence=1.0
            )]

    logger.info(
        f"got {len(elsets) - len(correlated_indices)} uncorrelated tracks")
    logger.info(f"got {len(correlated_indices)} correlated tracks")
    logger.info(f"got {len(tracks)} tracks")
    return tracks


if __name__ == "__main__":
    main()
